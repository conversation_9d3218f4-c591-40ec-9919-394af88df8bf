import cv2
import numpy as np
import os
import glob
import matplotlib.pyplot as plt

def load_sar_images(folder_path):
    """加载SAR图像"""
    pattern = os.path.join(folder_path, "*.tif")
    image_files = glob.glob(pattern)
    image_files.sort(key=lambda x: int(os.path.splitext(os.path.basename(x))[0]))
    
    images = []
    for img_path in image_files:
        img = cv2.imread(img_path, cv2.IMREAD_GRAYSCALE)
        if img is not None:
            images.append(img)
        else:
            print(f"无法加载图像: {img_path}")
    
    print(f"成功加载 {len(images)} 张图像")
    return images

def simple_frame_difference_detection(images, threshold=30, min_area=50):
    """简单的帧差法检测"""
    detections = []
    
    for i in range(1, len(images)):
        # 计算帧差
        diff = cv2.absdiff(images[i], images[i-1])
        
        # 高斯滤波去噪
        diff = cv2.GaussianBlur(diff, (5, 5), 0)
        
        # 二值化
        _, binary = cv2.threshold(diff, threshold, 255, cv2.THRESH_BINARY)
        
        # 形态学处理
        kernel = cv2.getStructuringElement(cv2.MORPH_ELLIPSE, (5, 5))
        binary = cv2.morphologyEx(binary, cv2.MORPH_CLOSE, kernel)
        binary = cv2.morphologyEx(binary, cv2.MORPH_OPEN, kernel)
        
        # 查找轮廓
        contours, _ = cv2.findContours(binary, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
        
        frame_detections = []
        for contour in contours:
            area = cv2.contourArea(contour)
            if area > min_area:
                x, y, w, h = cv2.boundingRect(contour)
                center_x = x + w // 2
                center_y = y + h // 2
                
                frame_detections.append({
                    'frame': i,
                    'center': (center_x, center_y),
                    'bbox': (x, y, w, h),
                    'area': area
                })
        
        detections.append(frame_detections)
        print(f"帧 {i+1}: 检测到 {len(frame_detections)} 个目标")
    
    return detections

def visualize_detection(image, detections, frame_idx, save_path=None):
    """可视化单帧检测结果"""
    img_color = cv2.cvtColor(image, cv2.COLOR_GRAY2BGR)
    
    for detection in detections:
        x, y, w, h = detection['bbox']
        center = detection['center']
        
        # 绘制边界框
        cv2.rectangle(img_color, (x, y), (x + w, y + h), (0, 255, 0), 2)
        
        # 绘制中心点
        cv2.circle(img_color, center, 3, (0, 0, 255), -1)
        
        # 添加面积信息
        cv2.putText(img_color, f"Area: {detection['area']:.0f}", 
                   (x, y - 10), cv2.FONT_HERSHEY_SIMPLEX, 0.5, (255, 255, 255), 1)
    
    plt.figure(figsize=(10, 8))
    plt.imshow(cv2.cvtColor(img_color, cv2.COLOR_BGR2RGB))
    plt.title(f'Frame {frame_idx + 1} - Detected Targets: {len(detections)}')
    plt.axis('off')
    
    if save_path:
        plt.savefig(save_path, dpi=150, bbox_inches='tight')
    
    plt.show()

def simple_tracking(detections, max_distance=50):
    """简单的目标跟踪"""
    tracks = []
    track_id = 0
    
    for frame_idx, frame_detections in enumerate(detections):
        if frame_idx == 0:
            # 初始化轨迹
            for detection in frame_detections:
                tracks.append({
                    'id': track_id,
                    'positions': [detection['center']],
                    'frames': [detection['frame']],
                    'active': True
                })
                track_id += 1
        else:
            # 数据关联
            unmatched = frame_detections.copy()
            
            for track in tracks:
                if not track['active']:
                    continue
                
                last_pos = track['positions'][-1]
                best_match = None
                min_dist = float('inf')
                
                for detection in unmatched:
                    dist = np.sqrt((detection['center'][0] - last_pos[0])**2 + 
                                 (detection['center'][1] - last_pos[1])**2)
                    
                    if dist < min_dist and dist < max_distance:
                        min_dist = dist
                        best_match = detection
                
                if best_match:
                    track['positions'].append(best_match['center'])
                    track['frames'].append(best_match['frame'])
                    unmatched.remove(best_match)
                else:
                    track['active'] = False
            
            # 创建新轨迹
            for detection in unmatched:
                tracks.append({
                    'id': track_id,
                    'positions': [detection['center']],
                    'frames': [detection['frame']],
                    'active': True
                })
                track_id += 1
    
    return tracks

def main():
    """主函数 - 简化版本"""
    print("=== SAR运动目标检测 (简化版) ===")
    
    # 加载图像
    images = load_sar_images('crop_region')
    if len(images) == 0:
        print("没有找到图像文件！")
        return
    
    print(f"图像尺寸: {images[0].shape}")
    
    # 运动目标检测
    print("\n开始检测运动目标...")
    detections = simple_frame_difference_detection(images, threshold=25, min_area=30)
    
    # 统计结果
    total_detections = sum(len(frame_det) for frame_det in detections)
    print(f"\n总共检测到 {total_detections} 个目标")
    
    # 目标跟踪
    print("\n开始目标跟踪...")
    tracks = simple_tracking(detections)
    valid_tracks = [track for track in tracks if len(track['positions']) >= 3]
    print(f"跟踪到 {len(valid_tracks)} 条有效轨迹")
    
    # 可视化部分结果
    print("\n可视化检测结果...")
    key_frames = [5, 10, 15, 20]
    for frame_idx in key_frames:
        if frame_idx < len(detections):
            visualize_detection(images[frame_idx+1], detections[frame_idx], 
                              frame_idx+1, f'simple_detection_frame_{frame_idx+1}.png')
    
    # 输出轨迹信息
    print(f"\n=== 轨迹分析结果 ===")
    for track in valid_tracks:
        print(f"轨迹 {track['id']}: {len(track['positions'])} 个点, "
              f"从帧 {track['frames'][0]+1} 到帧 {track['frames'][-1]+1}")
        print(f"  起始位置: {track['positions'][0]}")
        print(f"  结束位置: {track['positions'][-1]}")
    
    print("\n检测完成！检查生成的图像文件查看结果。")

if __name__ == "__main__":
    main()
