import cv2
import numpy as np
import os
import glob
import matplotlib.pyplot as plt

def load_sar_images(folder_path):
    """加载SAR图像"""
    pattern = os.path.join(folder_path, "*.tif")
    image_files = glob.glob(pattern)
    image_files.sort(key=lambda x: int(os.path.splitext(os.path.basename(x))[0]))
    
    images = []
    for img_path in image_files:
        img = cv2.imread(img_path, cv2.IMREAD_GRAYSCALE)
        if img is not None:
            images.append(img)
    
    return images

def detect_moving_targets(images, threshold=30, min_area=40):
    """运动目标检测"""
    detections = []
    
    for i in range(1, len(images)):
        # 计算帧差
        diff = cv2.absdiff(images[i], images[i-1])
        
        # 高斯滤波去噪
        diff = cv2.GaussianBlur(diff, (5, 5), 0)
        
        # 二值化
        _, binary = cv2.threshold(diff, threshold, 255, cv2.THRESH_BINARY)
        
        # 形态学处理
        kernel = cv2.getStructuringElement(cv2.MORPH_ELLIPSE, (5, 5))
        binary = cv2.morphologyEx(binary, cv2.MORPH_CLOSE, kernel)
        binary = cv2.morphologyEx(binary, cv2.MORPH_OPEN, kernel)
        
        # 查找轮廓
        contours, _ = cv2.findContours(binary, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
        
        frame_detections = []
        for contour in contours:
            area = cv2.contourArea(contour)
            if area > min_area:
                x, y, w, h = cv2.boundingRect(contour)
                center_x = x + w // 2
                center_y = y + h // 2
                
                frame_detections.append({
                    'frame': i,
                    'center': (center_x, center_y),
                    'bbox': (x, y, w, h),
                    'area': area
                })
        
        detections.append(frame_detections)
    
    return detections

def track_targets(detections, max_distance=50):
    """目标跟踪"""
    tracks = []
    track_id = 0
    
    for frame_idx, frame_detections in enumerate(detections):
        if frame_idx == 0:
            for detection in frame_detections:
                tracks.append({
                    'id': track_id,
                    'positions': [detection['center']],
                    'frames': [detection['frame']],
                    'areas': [detection['area']],
                    'active': True
                })
                track_id += 1
        else:
            unmatched = frame_detections.copy()
            
            for track in tracks:
                if not track['active']:
                    continue
                
                last_pos = track['positions'][-1]
                best_match = None
                min_dist = float('inf')
                
                for detection in unmatched:
                    dist = np.sqrt((detection['center'][0] - last_pos[0])**2 + 
                                 (detection['center'][1] - last_pos[1])**2)
                    
                    if dist < min_dist and dist < max_distance:
                        min_dist = dist
                        best_match = detection
                
                if best_match:
                    track['positions'].append(best_match['center'])
                    track['frames'].append(best_match['frame'])
                    track['areas'].append(best_match['area'])
                    unmatched.remove(best_match)
                else:
                    track['active'] = False
            
            for detection in unmatched:
                tracks.append({
                    'id': track_id,
                    'positions': [detection['center']],
                    'frames': [detection['frame']],
                    'areas': [detection['area']],
                    'active': True
                })
                track_id += 1
    
    return tracks

def filter_valid_tracks(tracks, min_length=10, min_movement=20):
    """过滤有效轨迹"""
    valid_tracks = []
    
    for track in tracks:
        if len(track['positions']) < min_length:
            continue
        
        positions = np.array(track['positions'])
        start_pos = positions[0]
        end_pos = positions[-1]
        
        # 计算总移动距离
        total_movement = np.sqrt((end_pos[0] - start_pos[0])**2 + 
                               (end_pos[1] - start_pos[1])**2)
        
        if total_movement > min_movement:
            # 计算轨迹特征
            track_length = len(positions)
            
            # 计算路径长度
            path_length = 0
            for i in range(1, len(positions)):
                path_length += np.sqrt((positions[i][0] - positions[i-1][0])**2 + 
                                     (positions[i][1] - positions[i-1][1])**2)
            
            # 计算直线度
            linearity = total_movement / path_length if path_length > 0 else 0
            
            # 计算平均面积
            avg_area = np.mean(track['areas'])
            
            # 计算评分
            score = (track_length * 2 + total_movement * 0.5 + linearity * 50 + 
                    min(avg_area / 100, 10))
            
            valid_tracks.append({
                'track': track,
                'total_movement': total_movement,
                'path_length': path_length,
                'linearity': linearity,
                'avg_area': avg_area,
                'score': score
            })
    
    # 按评分排序
    valid_tracks.sort(key=lambda x: x['score'], reverse=True)
    return valid_tracks

def visualize_results(images, valid_tracks, save_path=None):
    """可视化结果"""
    if not valid_tracks:
        print("没有找到有效的运动目标")
        return
    
    # 使用第一帧作为背景
    background = images[0].copy()
    background = cv2.cvtColor(background, cv2.COLOR_GRAY2BGR)
    
    colors = [(255, 0, 0), (0, 255, 0), (0, 0, 255), (255, 255, 0), 
             (255, 0, 255), (0, 255, 255), (128, 0, 128), (255, 165, 0)]
    
    plt.figure(figsize=(15, 10))
    
    print(f"\n=== 检测到的运动目标 ===")
    
    for i, valid_track in enumerate(valid_tracks[:8]):  # 显示前8个
        track = valid_track['track']
        color = colors[i % len(colors)]
        positions = np.array(track['positions'])
        
        # 绘制轨迹线
        for j in range(len(positions) - 1):
            cv2.line(background, tuple(positions[j]), tuple(positions[j + 1]), color, 2)
        
        # 绘制起始点和结束点
        cv2.circle(background, tuple(positions[0]), 6, (0, 255, 0), -1)  # 绿色起始点
        cv2.circle(background, tuple(positions[-1]), 6, (0, 0, 255), -1)  # 红色结束点
        
        # 添加标签
        cv2.putText(background, f'T{i+1}', tuple(positions[0]), 
                   cv2.FONT_HERSHEY_SIMPLEX, 0.6, color, 2)
        
        # 打印信息
        print(f"目标 {i+1}:")
        print(f"  轨迹长度: {len(positions)} 帧")
        print(f"  总移动距离: {valid_track['total_movement']:.1f} 像素")
        print(f"  路径长度: {valid_track['path_length']:.1f} 像素")
        print(f"  直线度: {valid_track['linearity']:.3f}")
        print(f"  平均面积: {valid_track['avg_area']:.1f}")
        print(f"  评分: {valid_track['score']:.1f}")
        print(f"  起始位置: {positions[0]}")
        print(f"  结束位置: {positions[-1]}")
        print()
    
    plt.imshow(cv2.cvtColor(background, cv2.COLOR_BGR2RGB))
    plt.title(f'SAR Moving Target Detection Results ({len(valid_tracks)} targets found)')
    plt.axis('off')
    
    if save_path:
        plt.savefig(save_path, dpi=150, bbox_inches='tight')
    
    plt.show()

def create_detection_video(images, valid_tracks, output_path='detection_video.avi'):
    """创建检测结果视频"""
    if not valid_tracks or len(images) == 0:
        return
    
    height, width = images[0].shape
    fourcc = cv2.VideoWriter_fourcc(*'XVID')
    out = cv2.VideoWriter(output_path, fourcc, 2.0, (width, height))
    
    colors = [(255, 0, 0), (0, 255, 0), (0, 0, 255), (255, 255, 0), 
             (255, 0, 255), (0, 255, 255), (128, 0, 128), (255, 165, 0)]
    
    for frame_idx, img in enumerate(images):
        frame = cv2.cvtColor(img, cv2.COLOR_GRAY2BGR)
        
        # 绘制当前帧的目标位置
        for i, valid_track in enumerate(valid_tracks[:5]):
            track = valid_track['track']
            color = colors[i % len(colors)]
            
            # 找到当前帧的位置
            for j, track_frame in enumerate(track['frames']):
                if track_frame == frame_idx:
                    pos = track['positions'][j]
                    cv2.circle(frame, pos, 8, color, -1)
                    cv2.putText(frame, f'T{i+1}', (pos[0]+10, pos[1]), 
                               cv2.FONT_HERSHEY_SIMPLEX, 0.5, color, 2)
                    break
        
        # 添加帧号
        cv2.putText(frame, f'Frame {frame_idx+1}', (10, 30), 
                   cv2.FONT_HERSHEY_SIMPLEX, 1, (255, 255, 255), 2)
        
        out.write(frame)
    
    out.release()
    print(f"检测视频已保存为: {output_path}")

def main():
    """主函数"""
    print("=== SAR运动目标检测系统 ===")
    
    # 加载图像
    images = load_sar_images('crop_region')
    if len(images) == 0:
        print("没有找到图像文件！")
        return
    
    print(f"成功加载 {len(images)} 张图像，尺寸: {images[0].shape}")
    
    # 检测运动目标
    print("\n开始检测运动目标...")
    detections = detect_moving_targets(images, threshold=25, min_area=50)
    
    total_detections = sum(len(frame_det) for frame_det in detections)
    print(f"总共检测到 {total_detections} 个候选目标")
    
    # 目标跟踪
    print("\n开始目标跟踪...")
    tracks = track_targets(detections, max_distance=60)
    
    # 过滤有效轨迹
    print("\n分析和过滤轨迹...")
    valid_tracks = filter_valid_tracks(tracks, min_length=8, min_movement=15)
    
    print(f"找到 {len(valid_tracks)} 个有效的运动目标")
    
    # 可视化结果
    visualize_results(images, valid_tracks, 'final_detection_results.png')
    
    # 创建检测视频
    create_detection_video(images, valid_tracks)
    
    # 保存详细结果
    with open('detection_summary.txt', 'w', encoding='utf-8') as f:
        f.write("SAR运动目标检测结果摘要\n")
        f.write("=" * 40 + "\n\n")
        f.write(f"输入图像数量: {len(images)}\n")
        f.write(f"图像尺寸: {images[0].shape}\n")
        f.write(f"检测到的候选目标总数: {total_detections}\n")
        f.write(f"有效运动目标数量: {len(valid_tracks)}\n\n")
        
        for i, valid_track in enumerate(valid_tracks):
            track = valid_track['track']
            positions = np.array(track['positions'])
            f.write(f"目标 {i+1}:\n")
            f.write(f"  轨迹ID: {track['id']}\n")
            f.write(f"  轨迹长度: {len(positions)} 帧\n")
            f.write(f"  总移动距离: {valid_track['total_movement']:.2f} 像素\n")
            f.write(f"  路径长度: {valid_track['path_length']:.2f} 像素\n")
            f.write(f"  直线度: {valid_track['linearity']:.3f}\n")
            f.write(f"  平均面积: {valid_track['avg_area']:.2f}\n")
            f.write(f"  评分: {valid_track['score']:.2f}\n")
            f.write(f"  起始位置: {positions[0]}\n")
            f.write(f"  结束位置: {positions[-1]}\n")
            f.write(f"  起始帧: {track['frames'][0]+1}\n")
            f.write(f"  结束帧: {track['frames'][-1]+1}\n")
            f.write("-" * 30 + "\n")
    
    print("\n检测完成！")
    print("生成的文件:")
    print("- final_detection_results.png: 检测结果可视化")
    print("- detection_video.avi: 检测过程视频")
    print("- detection_summary.txt: 详细检测报告")

if __name__ == "__main__":
    main()
