import cv2
import numpy as np
import os
import matplotlib.pyplot as plt
from skimage import io, filters, morphology, measure
from scipy import ndimage
import glob

class SARMovingTargetDetector:
    def __init__(self, image_folder):
        """
        SAR运动目标检测器

        Args:
            image_folder: 包含SAR图像的文件夹路径
        """
        self.image_folder = image_folder
        self.images = []
        self.image_paths = []
        self.background_model = None
        self.detections = []

    def load_images(self):
        """加载所有SAR图像"""
        # 获取所有tif文件并按数字顺序排序
        pattern = os.path.join(self.image_folder, "*.tif")
        image_files = glob.glob(pattern)

        # 按文件名中的数字排序
        image_files.sort(key=lambda x: int(os.path.splitext(os.path.basename(x))[0]))

        print(f"找到 {len(image_files)} 张图像")

        for img_path in image_files:
            try:
                # 读取图像
                img = io.imread(img_path)

                # 如果是多通道图像，转换为灰度图
                if len(img.shape) == 3:
                    img = cv2.cvtColor(img, cv2.COLOR_RGB2GRAY)

                # 归一化到0-255范围
                img = ((img - img.min()) / (img.max() - img.min()) * 255).astype(np.uint8)

                self.images.append(img)
                self.image_paths.append(img_path)

            except Exception as e:
                print(f"加载图像 {img_path} 时出错: {e}")

        print(f"成功加载 {len(self.images)} 张图像")
        return len(self.images) > 0

    def preprocess_image(self, image):
        """图像预处理"""
        # 高斯滤波去噪
        denoised = filters.gaussian(image, sigma=1.0)

        # 直方图均衡化增强对比度
        equalized = cv2.equalizeHist((denoised * 255).astype(np.uint8))

        return equalized

    def create_background_model(self, method='median', num_frames=10):
        """
        创建背景模型

        Args:
            method: 背景建模方法 ('median', 'mean', 'mog2')
            num_frames: 用于建模的帧数
        """
        if len(self.images) == 0:
            raise ValueError("请先加载图像")

        # 预处理图像
        processed_images = [self.preprocess_image(img) for img in self.images[:num_frames]]

        if method == 'median':
            # 使用中值法建立背景模型
            self.background_model = np.median(processed_images, axis=0).astype(np.uint8)
        elif method == 'mean':
            # 使用均值法建立背景模型
            self.background_model = np.mean(processed_images, axis=0).astype(np.uint8)
        elif method == 'mog2':
            # 使用MOG2背景减除器
            backSub = cv2.createBackgroundSubtractorMOG2(detectShadows=False)
            for img in processed_images:
                backSub.apply(img)
            self.background_model = backSub.getBackgroundImage()

        print(f"背景模型创建完成，使用方法: {method}")
        return self.background_model

    def detect_moving_targets_frame_diff(self, threshold=30, min_area=50):
        """
        使用帧差法检测运动目标

        Args:
            threshold: 差分阈值
            min_area: 最小目标面积
        """
        detections = []

        for i in range(1, len(self.images)):
            # 预处理当前帧和前一帧
            current_frame = self.preprocess_image(self.images[i])
            prev_frame = self.preprocess_image(self.images[i-1])

            # 计算帧差
            frame_diff = cv2.absdiff(current_frame, prev_frame)

            # 二值化
            _, binary = cv2.threshold(frame_diff, threshold, 255, cv2.THRESH_BINARY)

            # 形态学处理
            kernel = cv2.getStructuringElement(cv2.MORPH_ELLIPSE, (5, 5))
            binary = cv2.morphologyEx(binary, cv2.MORPH_CLOSE, kernel)
            binary = cv2.morphologyEx(binary, cv2.MORPH_OPEN, kernel)

            # 连通域分析
            contours, _ = cv2.findContours(binary, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)

            frame_detections = []
            for contour in contours:
                area = cv2.contourArea(contour)
                if area > min_area:
                    # 计算边界框
                    x, y, w, h = cv2.boundingRect(contour)
                    # 计算中心点
                    center_x = x + w // 2
                    center_y = y + h // 2

                    frame_detections.append({
                        'frame': i,
                        'center': (center_x, center_y),
                        'bbox': (x, y, w, h),
                        'area': area,
                        'confidence': area / (w * h)  # 填充率作为置信度
                    })

            detections.append(frame_detections)

        self.detections = detections
        return detections

    def detect_moving_targets_background_sub(self, threshold=50, min_area=50):
        """
        使用背景减除法检测运动目标

        Args:
            threshold: 前景检测阈值
            min_area: 最小目标面积
        """
        if self.background_model is None:
            print("创建背景模型...")
            self.create_background_model()

        detections = []

        for i, img in enumerate(self.images):
            # 预处理当前帧
            current_frame = self.preprocess_image(img)

            # 背景减除
            foreground = cv2.absdiff(current_frame, self.background_model)

            # 二值化
            _, binary = cv2.threshold(foreground, threshold, 255, cv2.THRESH_BINARY)

            # 形态学处理
            kernel = cv2.getStructuringElement(cv2.MORPH_ELLIPSE, (3, 3))
            binary = cv2.morphologyEx(binary, cv2.MORPH_CLOSE, kernel)
            binary = cv2.morphologyEx(binary, cv2.MORPH_OPEN, kernel)

            # 连通域分析
            contours, _ = cv2.findContours(binary, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)

            frame_detections = []
            for contour in contours:
                area = cv2.contourArea(contour)
                if area > min_area:
                    # 计算边界框
                    x, y, w, h = cv2.boundingRect(contour)
                    # 计算中心点
                    center_x = x + w // 2
                    center_y = y + h // 2

                    frame_detections.append({
                        'frame': i,
                        'center': (center_x, center_y),
                        'bbox': (x, y, w, h),
                        'area': area,
                        'confidence': area / (w * h)
                    })

            detections.append(frame_detections)

        self.detections = detections
        return detections

    def track_targets(self, max_distance=50):
        """
        简单的目标跟踪算法

        Args:
            max_distance: 最大关联距离
        """
        if not self.detections:
            print("请先运行目标检测")
            return []

        tracks = []
        track_id = 0

        for frame_idx, frame_detections in enumerate(self.detections):
            if frame_idx == 0:
                # 初始化轨迹
                for detection in frame_detections:
                    tracks.append({
                        'id': track_id,
                        'positions': [detection['center']],
                        'frames': [detection['frame']],
                        'bboxes': [detection['bbox']],
                        'areas': [detection['area']],
                        'active': True
                    })
                    track_id += 1
            else:
                # 数据关联
                unmatched_detections = frame_detections.copy()

                for track in tracks:
                    if not track['active']:
                        continue

                    last_pos = track['positions'][-1]
                    best_match = None
                    min_distance = float('inf')

                    for detection in unmatched_detections:
                        distance = np.sqrt((detection['center'][0] - last_pos[0])**2 +
                                         (detection['center'][1] - last_pos[1])**2)

                        if distance < min_distance and distance < max_distance:
                            min_distance = distance
                            best_match = detection

                    if best_match:
                        # 更新轨迹
                        track['positions'].append(best_match['center'])
                        track['frames'].append(best_match['frame'])
                        track['bboxes'].append(best_match['bbox'])
                        track['areas'].append(best_match['area'])
                        unmatched_detections.remove(best_match)
                    else:
                        # 轨迹丢失
                        track['active'] = False

                # 为未匹配的检测创建新轨迹
                for detection in unmatched_detections:
                    tracks.append({
                        'id': track_id,
                        'positions': [detection['center']],
                        'frames': [detection['frame']],
                        'bboxes': [detection['bbox']],
                        'areas': [detection['area']],
                        'active': True
                    })
                    track_id += 1

        return tracks

    def visualize_detections(self, frame_idx, save_path=None):
        """
        可视化检测结果

        Args:
            frame_idx: 要可视化的帧索引
            save_path: 保存路径
        """
        if frame_idx >= len(self.images):
            print(f"帧索引 {frame_idx} 超出范围")
            return

        # 获取原始图像
        img = self.images[frame_idx].copy()
        if len(img.shape) == 2:
            img = cv2.cvtColor(img, cv2.COLOR_GRAY2BGR)

        # 绘制检测结果
        if frame_idx < len(self.detections):
            for detection in self.detections[frame_idx]:
                x, y, w, h = detection['bbox']
                center = detection['center']

                # 绘制边界框
                cv2.rectangle(img, (x, y), (x + w, y + h), (0, 255, 0), 2)

                # 绘制中心点
                cv2.circle(img, center, 3, (0, 0, 255), -1)

                # 添加文本信息
                text = f"Area: {detection['area']:.0f}"
                cv2.putText(img, text, (x, y - 10), cv2.FONT_HERSHEY_SIMPLEX, 0.5, (255, 255, 255), 1)

        # 显示图像
        plt.figure(figsize=(12, 8))
        plt.imshow(cv2.cvtColor(img, cv2.COLOR_BGR2RGB))
        plt.title(f'Frame {frame_idx + 1} - Moving Target Detection')
        plt.axis('off')

        if save_path:
            plt.savefig(save_path, dpi=150, bbox_inches='tight')

        plt.show()

    def visualize_tracks(self, tracks, save_path=None):
        """
        可视化目标轨迹

        Args:
            tracks: 轨迹列表
            save_path: 保存路径
        """
        if not tracks:
            print("没有轨迹数据")
            return

        # 使用第一帧作为背景
        background = self.images[0].copy()
        if len(background.shape) == 2:
            background = cv2.cvtColor(background, cv2.COLOR_GRAY2BGR)

        # 为每个轨迹分配不同颜色
        colors = [(255, 0, 0), (0, 255, 0), (0, 0, 255), (255, 255, 0),
                 (255, 0, 255), (0, 255, 255), (128, 0, 128), (255, 165, 0)]

        plt.figure(figsize=(15, 10))

        for i, track in enumerate(tracks):
            if len(track['positions']) < 3:  # 过滤太短的轨迹
                continue

            color = colors[i % len(colors)]
            positions = np.array(track['positions'])

            # 绘制轨迹线
            for j in range(len(positions) - 1):
                cv2.line(background, tuple(positions[j]), tuple(positions[j + 1]), color, 2)

            # 绘制起始点和结束点
            cv2.circle(background, tuple(positions[0]), 5, (0, 255, 0), -1)  # 绿色起始点
            cv2.circle(background, tuple(positions[-1]), 5, (0, 0, 255), -1)  # 红色结束点

            # 添加轨迹ID
            cv2.putText(background, f'Track {track["id"]}',
                       tuple(positions[0]), cv2.FONT_HERSHEY_SIMPLEX, 0.6, color, 2)

        plt.imshow(cv2.cvtColor(background, cv2.COLOR_BGR2RGB))
        plt.title('Moving Target Tracks')
        plt.axis('off')

        if save_path:
            plt.savefig(save_path, dpi=150, bbox_inches='tight')

        plt.show()

    def save_results(self, tracks, output_file='detection_results.txt'):
        """
        保存检测结果到文件

        Args:
            tracks: 轨迹列表
            output_file: 输出文件名
        """
        with open(output_file, 'w', encoding='utf-8') as f:
            f.write("SAR运动目标检测结果\n")
            f.write("=" * 50 + "\n\n")

            f.write(f"总共检测到 {len(tracks)} 条轨迹\n\n")

            for track in tracks:
                f.write(f"轨迹 ID: {track['id']}\n")
                f.write(f"轨迹长度: {len(track['positions'])} 帧\n")
                f.write(f"起始帧: {track['frames'][0] + 1}\n")
                f.write(f"结束帧: {track['frames'][-1] + 1}\n")
                f.write(f"起始位置: {track['positions'][0]}\n")
                f.write(f"结束位置: {track['positions'][-1]}\n")
                f.write(f"平均面积: {np.mean(track['areas']):.2f}\n")
                f.write("-" * 30 + "\n")

        print(f"结果已保存到 {output_file}")


def main():
    """主函数"""
    # 创建检测器
    detector = SARMovingTargetDetector('crop_region')

    # 加载图像
    print("正在加载SAR图像...")
    if not detector.load_images():
        print("图像加载失败！")
        return

    print(f"成功加载 {len(detector.images)} 张图像")
    print(f"图像尺寸: {detector.images[0].shape}")

    # 方法1: 帧差法检测
    print("\n=== 使用帧差法检测运动目标 ===")
    detections_frame_diff = detector.detect_moving_targets_frame_diff(threshold=25, min_area=30)

    # 统计检测结果
    total_detections = sum(len(frame_det) for frame_det in detections_frame_diff)
    print(f"帧差法检测到 {total_detections} 个目标")

    # 目标跟踪
    print("\n=== 进行目标跟踪 ===")
    tracks_frame_diff = detector.track_targets(max_distance=80)
    valid_tracks = [track for track in tracks_frame_diff if len(track['positions']) >= 3]
    print(f"帧差法跟踪到 {len(valid_tracks)} 条有效轨迹")

    # 方法2: 背景减除法检测
    print("\n=== 使用背景减除法检测运动目标 ===")
    detections_bg_sub = detector.detect_moving_targets_background_sub(threshold=40, min_area=30)

    # 统计检测结果
    total_detections_bg = sum(len(frame_det) for frame_det in detections_bg_sub)
    print(f"背景减除法检测到 {total_detections_bg} 个目标")

    # 目标跟踪
    tracks_bg_sub = detector.track_targets(max_distance=80)
    valid_tracks_bg = [track for track in tracks_bg_sub if len(track['positions']) >= 3]
    print(f"背景减除法跟踪到 {len(valid_tracks_bg)} 条有效轨迹")

    # 可视化结果
    print("\n=== 可视化检测结果 ===")

    # 显示几个关键帧的检测结果
    key_frames = [5, 10, 15, 20, 25]
    for frame_idx in key_frames:
        if frame_idx < len(detector.images):
            detector.visualize_detections(frame_idx, f'detection_frame_{frame_idx+1}.png')

    # 可视化轨迹
    if valid_tracks:
        detector.visualize_tracks(valid_tracks, 'tracks_frame_diff.png')

    if valid_tracks_bg:
        # 重新设置检测结果为背景减除法的结果
        detector.detections = detections_bg_sub
        detector.visualize_tracks(valid_tracks_bg, 'tracks_bg_sub.png')

    # 保存结果
    print("\n=== 保存检测结果 ===")
    if valid_tracks:
        detector.save_results(valid_tracks, 'frame_diff_results.txt')
    if valid_tracks_bg:
        detector.save_results(valid_tracks_bg, 'bg_sub_results.txt')

    print("\n检测完成！")
    print(f"建议查看以下文件:")
    print("- detection_frame_*.png: 各帧检测结果")
    print("- tracks_*.png: 目标轨迹可视化")
    print("- *_results.txt: 详细检测结果")


if __name__ == "__main__":
    main()