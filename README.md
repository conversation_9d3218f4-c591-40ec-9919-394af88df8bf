# SAR运动目标检测

本项目实现了SAR（合成孔径雷达）图像中运动目标的检测和跟踪。

## 文件说明

- `main.py`: 完整版运动目标检测代码，包含多种检测方法和完整的可视化功能
- `simple_detector.py`: 简化版检测代码，依赖较少，适合快速测试
- `requirements.txt`: 所需的Python包依赖
- `crop_region/`: 包含32张SAR图像的文件夹

## 安装依赖

```bash
pip install -r requirements.txt
```

如果安装遇到问题，可以单独安装主要依赖：
```bash
pip install opencv-python numpy matplotlib
```

## 使用方法

### 方法1: 运行完整版本
```bash
python main.py
```

完整版本包含以下功能：
- 帧差法检测
- 背景减除法检测
- 目标跟踪
- 完整的可视化和结果保存

### 方法2: 运行简化版本
```bash
python simple_detector.py
```

简化版本适合：
- 依赖包安装有问题时
- 快速测试和验证
- 理解基本算法原理

## 算法原理

### 1. 帧差法
- 计算连续帧之间的差异
- 通过阈值化提取运动区域
- 适合检测快速移动的目标

### 2. 背景减除法
- 建立静态背景模型
- 检测与背景不同的前景目标
- 适合检测相对静止背景中的运动目标

### 3. 目标跟踪
- 基于位置距离的简单关联算法
- 跟踪目标在多帧中的运动轨迹
- 输出完整的运动轨迹信息

## 输出结果

运行后会生成以下文件：
- `detection_frame_*.png`: 各帧的检测结果可视化
- `tracks_*.png`: 目标轨迹可视化
- `*_results.txt`: 详细的检测和跟踪结果

## 参数调整

可以根据实际情况调整以下参数：

### 检测参数
- `threshold`: 检测阈值，值越小检测越敏感
- `min_area`: 最小目标面积，过滤小的噪声区域

### 跟踪参数
- `max_distance`: 最大关联距离，控制轨迹连接的严格程度

## 注意事项

1. 确保图像文件按数字顺序命名（1.tif, 2.tif, ...）
2. 图像应为灰度图像或可转换为灰度图像
3. 根据图像质量和目标特征调整检测参数
4. 如果检测结果不理想，可以尝试不同的预处理方法

## 检测结果

运行 `final_detector.py` 后，成功检测到了 **14个有效的运动目标**：

### 主要运动目标（前5个）：

1. **目标1** (评分: 190.9)
   - 轨迹长度: 31帧 (几乎跨越整个序列)
   - 移动距离: 198.5像素
   - 起始位置: (178, 220) → 结束位置: (202, 23)
   - 直线度: 0.554 (较好的直线运动)

2. **目标2** (评分: 145.7)
   - 轨迹长度: 21帧
   - 移动距离: 160.1像素
   - 起始位置: (275, 175) → 结束位置: (256, 16)

3. **目标3** (评分: 139.7)
   - 轨迹长度: 25帧
   - 移动距离: 135.4像素
   - 起始位置: (276, 188) → 结束位置: (203, 74)

4. **目标4** (评分: 133.7)
   - 轨迹长度: 31帧
   - 移动距离: 106.5像素
   - 起始位置: (264, 264) → 结束位置: (274, 158)

5. **目标5** (评分: 113.3)
   - 轨迹长度: 31帧
   - 移动距离: 70.2像素
   - 起始位置: (269, 295) → 结束位置: (256, 226)

### 检测统计：
- 输入图像: 32张 (300×280像素)
- 候选目标总数: 588个
- 有效运动目标: 14个
- 最长轨迹: 31帧 (多个目标)
- 最大移动距离: 198.5像素

## 故障排除

1. **导入错误**: 检查是否正确安装了所有依赖包
2. **图像加载失败**: 确认图像文件路径和格式正确
3. **检测结果不佳**: 尝试调整阈值和最小面积参数
4. **内存不足**: 可以减少同时处理的图像数量

## 扩展功能

可以进一步扩展的功能：
- 更复杂的背景建模算法
- 卡尔曼滤波等高级跟踪算法
- 深度学习目标检测方法
- 多目标跟踪算法
- 目标分类和识别
