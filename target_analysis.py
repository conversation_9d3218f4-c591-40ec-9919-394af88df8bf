import cv2
import numpy as np
import os
import glob
import matplotlib.pyplot as plt

def load_sar_images(folder_path):
    """加载SAR图像"""
    pattern = os.path.join(folder_path, "*.tif")
    image_files = glob.glob(pattern)
    image_files.sort(key=lambda x: int(os.path.splitext(os.path.basename(x))[0]))
    
    images = []
    for img_path in image_files:
        img = cv2.imread(img_path, cv2.IMREAD_GRAYSCALE)
        if img is not None:
            images.append(img)
    
    return images

def enhanced_detection(images, threshold=25, min_area=30):
    """增强的运动目标检测"""
    detections = []
    
    for i in range(1, len(images)):
        # 计算帧差
        diff = cv2.absdiff(images[i], images[i-1])
        
        # 多尺度高斯滤波
        diff_blur1 = cv2.GaussianBlur(diff, (3, 3), 0)
        diff_blur2 = cv2.GaussianBlur(diff, (7, 7), 0)
        diff_enhanced = cv2.addWeighted(diff_blur1, 0.7, diff_blur2, 0.3, 0)
        
        # 自适应阈值
        binary = cv2.adaptiveThreshold(diff_enhanced, 255, cv2.ADAPTIVE_THRESH_GAUSSIAN_C, 
                                     cv2.THRESH_BINARY, 11, 2)
        
        # 形态学处理
        kernel = cv2.getStructuringElement(cv2.MORPH_ELLIPSE, (3, 3))
        binary = cv2.morphologyEx(binary, cv2.MORPH_CLOSE, kernel)
        binary = cv2.morphologyEx(binary, cv2.MORPH_OPEN, kernel)
        
        # 查找轮廓
        contours, _ = cv2.findContours(binary, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
        
        frame_detections = []
        for contour in contours:
            area = cv2.contourArea(contour)
            if area > min_area:
                x, y, w, h = cv2.boundingRect(contour)
                center_x = x + w // 2
                center_y = y + h // 2
                
                # 计算形状特征
                aspect_ratio = w / h if h > 0 else 0
                extent = area / (w * h) if w * h > 0 else 0
                
                frame_detections.append({
                    'frame': i,
                    'center': (center_x, center_y),
                    'bbox': (x, y, w, h),
                    'area': area,
                    'aspect_ratio': aspect_ratio,
                    'extent': extent
                })
        
        detections.append(frame_detections)
    
    return detections

def advanced_tracking(detections, max_distance=60):
    """改进的目标跟踪算法"""
    tracks = []
    track_id = 0
    
    for frame_idx, frame_detections in enumerate(detections):
        if frame_idx == 0:
            # 初始化轨迹
            for detection in frame_detections:
                tracks.append({
                    'id': track_id,
                    'positions': [detection['center']],
                    'frames': [detection['frame']],
                    'areas': [detection['area']],
                    'velocities': [],
                    'active': True,
                    'confidence': 1.0
                })
                track_id += 1
        else:
            # 预测下一位置（简单线性预测）
            for track in tracks:
                if track['active'] and len(track['positions']) >= 2:
                    last_pos = track['positions'][-1]
                    prev_pos = track['positions'][-2]
                    predicted_pos = (
                        last_pos[0] + (last_pos[0] - prev_pos[0]),
                        last_pos[1] + (last_pos[1] - prev_pos[1])
                    )
                    track['predicted_pos'] = predicted_pos
                else:
                    track['predicted_pos'] = track['positions'][-1] if track['positions'] else (0, 0)
            
            # 数据关联（使用预测位置）
            unmatched = frame_detections.copy()
            
            for track in tracks:
                if not track['active']:
                    continue
                
                predicted_pos = track['predicted_pos']
                best_match = None
                min_dist = float('inf')
                
                for detection in unmatched:
                    dist = np.sqrt((detection['center'][0] - predicted_pos[0])**2 + 
                                 (detection['center'][1] - predicted_pos[1])**2)
                    
                    if dist < min_dist and dist < max_distance:
                        min_dist = dist
                        best_match = detection
                
                if best_match:
                    # 更新轨迹
                    track['positions'].append(best_match['center'])
                    track['frames'].append(best_match['frame'])
                    track['areas'].append(best_match['area'])
                    
                    # 计算速度
                    if len(track['positions']) >= 2:
                        last_pos = track['positions'][-1]
                        prev_pos = track['positions'][-2]
                        velocity = np.sqrt((last_pos[0] - prev_pos[0])**2 + 
                                         (last_pos[1] - prev_pos[1])**2)
                        track['velocities'].append(velocity)
                    
                    # 更新置信度
                    track['confidence'] = min(track['confidence'] + 0.1, 1.0)
                    unmatched.remove(best_match)
                else:
                    # 轨迹丢失，降低置信度
                    track['confidence'] -= 0.3
                    if track['confidence'] <= 0:
                        track['active'] = False
            
            # 创建新轨迹
            for detection in unmatched:
                tracks.append({
                    'id': track_id,
                    'positions': [detection['center']],
                    'frames': [detection['frame']],
                    'areas': [detection['area']],
                    'velocities': [],
                    'active': True,
                    'confidence': 0.5
                })
                track_id += 1
    
    return tracks

def analyze_tracks(tracks):
    """分析轨迹特征，识别真实运动目标"""
    analyzed_tracks = []
    
    for track in tracks:
        if len(track['positions']) < 5:  # 过滤太短的轨迹
            continue
        
        positions = np.array(track['positions'])
        
        # 计算轨迹特征
        track_length = len(positions)
        total_distance = 0
        for i in range(1, len(positions)):
            total_distance += np.sqrt((positions[i][0] - positions[i-1][0])**2 + 
                                    (positions[i][1] - positions[i-1][1])**2)
        
        # 计算直线度（轨迹的直线程度）
        start_pos = positions[0]
        end_pos = positions[-1]
        straight_distance = np.sqrt((end_pos[0] - start_pos[0])**2 + 
                                  (end_pos[1] - start_pos[1])**2)
        linearity = straight_distance / total_distance if total_distance > 0 else 0
        
        # 计算平均速度和速度变化
        avg_velocity = np.mean(track['velocities']) if track['velocities'] else 0
        velocity_std = np.std(track['velocities']) if track['velocities'] else 0
        
        # 计算面积变化
        avg_area = np.mean(track['areas'])
        area_std = np.std(track['areas'])
        
        # 综合评分（运动目标可能性）
        score = 0
        
        # 轨迹长度评分（越长越好）
        length_score = min(track_length / 20, 1.0) * 30
        
        # 运动一致性评分（速度变化小更好）
        consistency_score = max(0, 1 - velocity_std / max(avg_velocity, 1)) * 25
        
        # 直线度评分（适度的直线度更好）
        linearity_score = (1 - abs(linearity - 0.7)) * 20
        
        # 面积稳定性评分
        area_stability_score = max(0, 1 - area_std / max(avg_area, 1)) * 15
        
        # 置信度评分
        confidence_score = track['confidence'] * 10
        
        score = length_score + consistency_score + linearity_score + area_stability_score + confidence_score
        
        analyzed_tracks.append({
            'track': track,
            'track_length': track_length,
            'total_distance': total_distance,
            'straight_distance': straight_distance,
            'linearity': linearity,
            'avg_velocity': avg_velocity,
            'velocity_std': velocity_std,
            'avg_area': avg_area,
            'area_std': area_std,
            'score': score
        })
    
    # 按评分排序
    analyzed_tracks.sort(key=lambda x: x['score'], reverse=True)
    
    return analyzed_tracks

def visualize_top_targets(images, analyzed_tracks, top_n=5, save_path=None):
    """可视化最可能的运动目标"""
    if not analyzed_tracks:
        print("没有轨迹数据")
        return
    
    # 使用第一帧作为背景
    background = images[0].copy()
    background = cv2.cvtColor(background, cv2.COLOR_GRAY2BGR)
    
    # 颜色列表
    colors = [(255, 0, 0), (0, 255, 0), (0, 0, 255), (255, 255, 0), 
             (255, 0, 255), (0, 255, 255), (128, 0, 128), (255, 165, 0)]
    
    plt.figure(figsize=(15, 10))
    
    print(f"\n=== 前 {top_n} 个最可能的运动目标 ===")
    
    for i, analyzed_track in enumerate(analyzed_tracks[:top_n]):
        track = analyzed_track['track']
        color = colors[i % len(colors)]
        positions = np.array(track['positions'])
        
        # 绘制轨迹线
        for j in range(len(positions) - 1):
            cv2.line(background, tuple(positions[j]), tuple(positions[j + 1]), color, 3)
        
        # 绘制起始点和结束点
        cv2.circle(background, tuple(positions[0]), 8, (0, 255, 0), -1)  # 绿色起始点
        cv2.circle(background, tuple(positions[-1]), 8, (0, 0, 255), -1)  # 红色结束点
        
        # 添加轨迹ID和评分
        cv2.putText(background, f'Target {i+1} (Score: {analyzed_track["score"]:.1f})', 
                   tuple(positions[0]), cv2.FONT_HERSHEY_SIMPLEX, 0.7, color, 2)
        
        # 打印详细信息
        print(f"目标 {i+1}:")
        print(f"  评分: {analyzed_track['score']:.2f}")
        print(f"  轨迹长度: {analyzed_track['track_length']} 帧")
        print(f"  总移动距离: {analyzed_track['total_distance']:.2f} 像素")
        print(f"  直线距离: {analyzed_track['straight_distance']:.2f} 像素")
        print(f"  直线度: {analyzed_track['linearity']:.3f}")
        print(f"  平均速度: {analyzed_track['avg_velocity']:.2f} 像素/帧")
        print(f"  起始位置: {positions[0]}")
        print(f"  结束位置: {positions[-1]}")
        print()
    
    plt.imshow(cv2.cvtColor(background, cv2.COLOR_BGR2RGB))
    plt.title(f'Top {top_n} Most Likely Moving Targets')
    plt.axis('off')
    
    if save_path:
        plt.savefig(save_path, dpi=150, bbox_inches='tight')
    
    plt.show()

def main():
    """主函数 - 目标分析版本"""
    print("=== SAR运动目标分析 ===")
    
    # 加载图像
    images = load_sar_images('crop_region')
    if len(images) == 0:
        print("没有找到图像文件！")
        return
    
    print(f"成功加载 {len(images)} 张图像，尺寸: {images[0].shape}")
    
    # 增强检测
    print("\n开始增强检测...")
    detections = enhanced_detection(images, threshold=20, min_area=25)
    
    total_detections = sum(len(frame_det) for frame_det in detections)
    print(f"总共检测到 {total_detections} 个候选目标")
    
    # 改进跟踪
    print("\n开始改进跟踪...")
    tracks = advanced_tracking(detections, max_distance=70)
    
    # 分析轨迹
    print("\n分析轨迹特征...")
    analyzed_tracks = analyze_tracks(tracks)
    
    print(f"分析了 {len(analyzed_tracks)} 条有效轨迹")
    
    # 可视化最可能的目标
    visualize_top_targets(images, analyzed_tracks, top_n=5, save_path='top_targets.png')
    
    print("\n分析完成！查看 'top_targets.png' 文件查看结果。")

if __name__ == "__main__":
    main()
